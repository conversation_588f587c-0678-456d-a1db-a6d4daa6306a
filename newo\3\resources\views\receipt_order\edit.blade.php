@extends('layouts.admin')

@section('page-title')
    {{ __('تحرير أمر الاستلام') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('receipt-order.index') }}">{{ __('أوامر الاستلام') }}</a></li>
    <li class="breadcrumb-item">{{ __('تحرير أمر الاستلام') }}</li>
@endsection

@push('css-page')
<style>
    .product-row {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        margin-bottom: 10px;
        padding: 15px;
    }
    .product-sku {
        font-size: 0.8em;
        color: #6c757d;
    }
    .btn-remove-product {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    .btn-remove-product:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }
    .btn-remove-product:focus {
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    .summary-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    .summary-item {
        text-align: center;
        padding: 10px;
    }
    .summary-value {
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .summary-label {
        font-size: 0.9em;
        opacity: 0.8;
    }

    /* تحسينات إضافية للحقول */
    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .quantity-input:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .unit-cost-input:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }

    .product-row {
        transition: all 0.3s ease;
    }

    .product-row:hover {
        background-color: #f1f3f4;
        border-color: #dee2e6;
    }
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="mb-0">{{ __('تحرير أمر الاستلام') }} - {{ $receiptOrder->order_number }}</h5>
                    </div>
                    <div class="col-6 text-end">
                        <span class="badge badge-{{ $receiptOrder->status_color }}">{{ $receiptOrder->status }}</span>
                    </div>
                </div>
            </div>

            <form action="{{ route('receipt-order.update', $receiptOrder->id) }}" method="POST" id="receipt-order-form">
                @csrf
                @method('PUT')
                
                <div class="card-body">
                    <!-- معلومات الأمر الأساسية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vendor_id" class="form-label">{{ __('المورد') }}</label>
                                <select name="vendor_id" id="vendor_id" class="form-select">
                                    <option value="">{{ __('اختر المورد') }}</option>
                                    @foreach($vendors as $vendor)
                                        <option value="{{ $vendor->id }}" 
                                            {{ $receiptOrder->vendor_id == $vendor->id ? 'selected' : '' }}>
                                            {{ $vendor->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <small class="text-muted">{{ __('اسم المورد سيظهر في تقارير العمليات المالية') }}</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_id" class="form-label">{{ __('المستودع') }}</label>
                                <select name="warehouse_id" id="warehouse_id" class="form-select" required>
                                    @foreach($warehouses as $warehouse)
                                        <option value="{{ $warehouse->id }}" 
                                            {{ $receiptOrder->warehouse_id == $warehouse->id ? 'selected' : '' }}>
                                            {{ $warehouse->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_number" class="form-label">{{ __('رقم الفاتورة') }}</label>
                                <input type="text" name="invoice_number" id="invoice_number" 
                                       class="form-control" value="{{ $receiptOrder->invoice_number }}">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_total" class="form-label">{{ __('إجمالي الفاتورة') }}</label>
                                <input type="number" name="invoice_total" id="invoice_total" 
                                       class="form-control" step="0.01" min="0" 
                                       value="{{ $receiptOrder->invoice_total }}">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_date" class="form-label">{{ __('تاريخ الفاتورة') }}</label>
                                <input type="date" name="invoice_date" id="invoice_date" 
                                       class="form-control" value="{{ $receiptOrder->invoice_date ? $receiptOrder->invoice_date->format('Y-m-d') : '' }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="notes" class="form-label">{{ __('ملاحظات') }}</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3">{{ $receiptOrder->notes }}</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المنتجات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">{{ __('المنتجات') }}</h6>
                                    <button type="button" id="add-product-btn" class="btn btn-success btn-sm">
                                        <i class="ti ti-plus"></i> {{ __('إضافة منتج') }}
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm ms-2" onclick="testJS()">
                                        <i class="ti ti-bug"></i> اختبار
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="products-container">
                                        @foreach($receiptOrder->products as $index => $product)
                                            <div class="product-row" data-index="{{ $index }}">
                                                <div class="row align-items-end">
                                                    <div class="col-md-3">
                                                        <label class="form-label">{{ __('المنتج') }}</label>
                                                        <select name="products[{{ $index }}][product_id]" class="form-select product-select" required>
                                                            @foreach($products as $prod)
                                                                <option value="{{ $prod->id }}" 
                                                                    {{ $product->product_id == $prod->id ? 'selected' : '' }}
                                                                    data-sku="{{ $prod->sku }}"
                                                                    data-price="{{ $prod->purchase_price }}">
                                                                    {{ $prod->name }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                        <small class="text-muted product-sku">{{ $product->product->sku ?? '' }}</small>
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label">{{ __('الكمية') }}</label>
                                                        <input type="number" name="products[{{ $index }}][quantity]" 
                                                               class="form-control quantity-input" 
                                                               value="{{ $product->quantity }}" 
                                                               min="0.01" step="0.01" required>
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label">{{ __('سعر الوحدة') }}</label>
                                                        <input type="number" name="products[{{ $index }}][unit_cost]" 
                                                               class="form-control unit-cost-input" 
                                                               value="{{ $product->unit_cost }}" 
                                                               min="0" step="0.01">
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label">{{ __('تاريخ الصلاحية') }}</label>
                                                        <input type="date" name="products[{{ $index }}][expiry_date]" 
                                                               class="form-control" 
                                                               value="{{ $product->expiry_date ? $product->expiry_date->format('Y-m-d') : '' }}">
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label">{{ __('الإجمالي') }}</label>
                                                        <input type="text" class="form-control row-total" readonly 
                                                               value="{{ number_format($product->total_cost, 2) }}">
                                                    </div>

                                                    <div class="col-md-1">
                                                        <button type="button" class="btn btn-remove-product btn-sm" onclick="removeProduct(this)">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                @if($product->notes)
                                                <div class="row mt-2">
                                                    <div class="col-12">
                                                        <label class="form-label">{{ __('ملاحظات') }}</label>
                                                        <input type="text" name="products[{{ $index }}][notes]" 
                                                               class="form-control" value="{{ $product->notes }}">
                                                    </div>
                                                </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- ملخص الإجماليات -->
                                    <div class="summary-card">
                                        <div class="row">
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-products">{{ $receiptOrder->total_products }}</div>
                                                <div class="summary-label">{{ __('عدد المنتجات') }}</div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-quantity">0</div>
                                                <div class="summary-label">{{ __('إجمالي الكمية') }}</div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="average-cost">0</div>
                                                <div class="summary-label">{{ __('متوسط التكلفة') }}</div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-amount">{{ number_format($receiptOrder->total_amount, 2) }}</div>
                                                <div class="summary-label">{{ __('إجمالي المبلغ') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="row">
                        <div class="col-6">
                            <a href="{{ route('receipt-order.show', $receiptOrder->id) }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> {{ __('إلغاء') }}
                            </a>
                        </div>
                        <div class="col-6 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy"></i> {{ __('حفظ التغييرات') }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    let productIndex = {{ count($receiptOrder->products) }};

    console.log('Script loaded, productIndex:', productIndex);
    console.log('jQuery version:', $.fn.jquery);

    // دالة حذف المنتج المباشرة - نسخة محسنة
    window.removeProduct = function(button) {
        console.log('removeProduct called');
        try {
            // استخدام jQuery إذا كان متاحاً، وإلا استخدام vanilla JS
            let productRow, totalRows;

            if (typeof $ !== 'undefined') {
                // استخدام jQuery
                productRow = $(button).closest('.product-row');
                totalRows = $('.product-row').length;
                console.log('Using jQuery - Total rows:', totalRows);
            } else {
                // استخدام vanilla JavaScript
                productRow = button.closest('.product-row');
                totalRows = document.querySelectorAll('.product-row').length;
                console.log('Using vanilla JS - Total rows:', totalRows);
            }

            if (totalRows > 1) {
                let productName = 'المنتج المحدد';
                try {
                    if (typeof $ !== 'undefined') {
                        productName = productRow.find('.product-select option:selected').text() || 'المنتج المحدد';
                    } else {
                        const select = productRow.querySelector('.product-select');
                        productName = select ? select.options[select.selectedIndex].text : 'المنتج المحدد';
                    }
                } catch (e) {
                    console.log('Could not get product name:', e);
                }

                console.log('Product name:', productName);

                if (confirm('هل أنت متأكد من حذف "' + productName + '"؟')) {
                    console.log('User confirmed deletion');

                    if (typeof $ !== 'undefined') {
                        productRow.remove();
                    } else {
                        productRow.remove();
                    }

                    reindexProducts();
                    calculateTotals();
                    console.log('Product removed successfully');
                }
            } else {
                alert('يجب أن يحتوي الأمر على منتج واحد على الأقل');
            }
        } catch (error) {
            console.error('Error in removeProduct:', error);
            alert('حدث خطأ أثناء حذف المنتج: ' + error.message);
        }
    };

    // إضافة منتج جديد
    $('#add-product-btn').on('click', function() {
        const newIndex = $('.product-row').length;
        const productRow = `
            <div class="product-row" data-index="${newIndex}">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">{{ __('المنتج') }}</label>
                        <select name="products[${newIndex}][product_id]" class="form-select product-select" required>
                            <option value="">{{ __('اختر المنتج') }}</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}"
                                    data-sku="{{ $product->sku }}"
                                    data-price="{{ $product->purchase_price }}">
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                        <small class="text-muted product-sku"></small>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">{{ __('الكمية') }}</label>
                        <input type="number" name="products[${newIndex}][quantity]"
                               class="form-control quantity-input"
                               min="0.01" step="0.01" value="1" required>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">{{ __('سعر الوحدة') }}</label>
                        <input type="number" name="products[${newIndex}][unit_cost]"
                               class="form-control unit-cost-input"
                               min="0" step="0.01" value="0">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">{{ __('تاريخ الصلاحية') }}</label>
                        <input type="date" name="products[${newIndex}][expiry_date]"
                               class="form-control">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">{{ __('الإجمالي') }}</label>
                        <input type="text" class="form-control row-total" readonly value="0.00">
                    </div>

                    <div class="col-md-1">
                        <button type="button" class="btn btn-remove-product btn-sm" title="{{ __('حذف المنتج') }}" onclick="removeProduct(this)">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-12">
                        <label class="form-label">{{ __('ملاحظات') }}</label>
                        <input type="text" name="products[${newIndex}][notes]"
                               class="form-control" placeholder="{{ __('ملاحظات اختيارية') }}">
                    </div>
                </div>
            </div>
        `;

        $('#products-container').append(productRow);
        productIndex = $('.product-row').length;
        calculateTotals();

        // التركيز على المنتج الجديد
        $('#products-container .product-row:last .product-select').focus();
    });

    // حذف منتج - نسخة مبسطة
    $(document).on('click', '.btn-remove-product', function(e) {
        e.preventDefault();
        console.log('Delete button clicked');

        const productRow = $(this).closest('.product-row');
        const totalRows = $('.product-row').length;

        console.log('Total rows:', totalRows);

        if (totalRows > 1) {
            const productName = productRow.find('.product-select option:selected').text() || 'المنتج المحدد';

            if (confirm('هل أنت متأكد من حذف "' + productName + '"؟')) {
                console.log('Removing product row');
                productRow.remove();
                reindexProducts();
                calculateTotals();
                console.log('Product removed successfully');
            }
        } else {
            alert('يجب أن يحتوي الأمر على منتج واحد على الأقل');
        }

        return false;
    });

    // تحديث بيانات المنتج عند الاختيار
    $(document).on('change', '.product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const sku = selectedOption.data('sku');
        const price = selectedOption.data('price');

        const productRow = $(this).closest('.product-row');
        productRow.find('.product-sku').text(sku || '');
        productRow.find('.unit-cost-input').val(price || 0);

        calculateRowTotal(productRow);
    });

    // حساب إجمالي الصف عند تغيير الكمية أو السعر
    $(document).on('input', '.quantity-input, .unit-cost-input', function() {
        const productRow = $(this).closest('.product-row');
        calculateRowTotal(productRow);
    });

    // إعادة ترقيم المنتجات بعد الحذف
    function reindexProducts() {
        console.log('Reindexing products...');
        $('.product-row').each(function(index) {
            const row = $(this);
            row.attr('data-index', index);

            // تحديث أسماء الحقول
            row.find('select[name*="product_id"]').attr('name', `products[${index}][product_id]`);
            row.find('input[name*="quantity"]').attr('name', `products[${index}][quantity]`);
            row.find('input[name*="unit_cost"]').attr('name', `products[${index}][unit_cost]`);
            row.find('input[name*="expiry_date"]').attr('name', `products[${index}][expiry_date]`);
            row.find('input[name*="notes"]').attr('name', `products[${index}][notes]`);
        });

        // تحديث العداد
        productIndex = $('.product-row').length;
        console.log('Reindexing complete. New productIndex:', productIndex);
    }

    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
        const unitCost = parseFloat(row.find('.unit-cost-input').val()) || 0;
        const total = quantity * unitCost;

        row.find('.row-total').val(total.toFixed(2));
        calculateTotals();
    }

    // حساب الإجماليات العامة
    function calculateTotals() {
        let totalProducts = $('.product-row').length;
        let totalQuantity = 0;
        let totalAmount = 0;

        $('.product-row').each(function() {
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            const rowTotal = parseFloat($(this).find('.row-total').val()) || 0;

            totalQuantity += quantity;
            totalAmount += rowTotal;
        });

        const averageCost = totalQuantity > 0 ? (totalAmount / totalQuantity) : 0;

        $('#total-products').text(totalProducts);
        $('#total-quantity').text(totalQuantity.toFixed(2));
        $('#average-cost').text(averageCost.toFixed(2));
        $('#total-amount').text(totalAmount.toFixed(2));

        // إظهار/إخفاء رسالة عدم وجود منتجات
        if (totalProducts === 0) {
            if ($('#no-products-message').length === 0) {
                $('#products-container').append(`
                    <div id="no-products-message" class="alert alert-warning text-center">
                        <i class="ti ti-alert-triangle me-2"></i>
                        {{ __('لا توجد منتجات. اضغط على "إضافة منتج" لإضافة منتجات جديدة.') }}
                    </div>
                `);
            }
        } else {
            $('#no-products-message').remove();
        }
    }

    // حساب الإجماليات عند تحميل الصفحة
    $('.product-row').each(function() {
        calculateRowTotal($(this));
    });

    // التحقق من صحة النموذج قبل الإرسال
    $('#receipt-order-form').on('submit', function(e) {
        const productRows = $('.product-row').length;

        if (productRows === 0) {
            e.preventDefault();
            alert('{{ __('يجب إضافة منتج واحد على الأقل') }}');
            return false;
        }

        // التحقق من أن جميع المنتجات لها كمية
        let hasError = false;
        $('.product-row').each(function() {
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            if (quantity <= 0) {
                hasError = true;
                $(this).find('.quantity-input').addClass('is-invalid');
            } else {
                $(this).find('.quantity-input').removeClass('is-invalid');
            }
        });

        if (hasError) {
            e.preventDefault();
            alert('{{ __('يجب إدخال كمية صحيحة لجميع المنتجات') }}');
            return false;
        }
    });
});
</script>
@endpush
